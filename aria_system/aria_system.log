2025-06-23 23:54:30,916 - __main__ - INFO - Initializing database...
2025-06-23 23:54:30,963 - aria_system.database.supabase_client - INFO - Schema file found. Please execute the schema manually in Supabase SQL editor.
2025-06-23 23:54:30,964 - aria_system.database.supabase_client - INFO - Schema file location: /Users/<USER>/Development/zurich-UC05-claims-liability/zurich-UC05/aria_system/database/supabase_schema.sql
2025-06-23 23:54:30,964 - __main__ - INFO - Database initialized successfully
2025-06-24 00:24:29,208 - __main__ - INFO - Starting ARIA API server on 0.0.0.0:8000
2025-06-24 00:24:29,317 - main - INFO - Starting ARIA Claims Processing System...
2025-06-24 00:24:29,450 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 00:24:29,503 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 00:24:29,590 - main - INFO - <PERSON> system started successfully
2025-06-24 00:24:29,590 - aria_system.core.workflow_orchestrator - INFO - Starting ARIA Workflow Orchestrator...
2025-06-24 00:24:29,591 - aria_system.services.email_monitor - INFO - Starting email monitoring service...
2025-06-24 00:24:31,633 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:24:31,634 - aria_system.services.slack_service - ERROR - Failed to start Slack bot: name 'AsyncSocketModeHandler' is not defined
2025-06-24 00:24:31,634 - main - INFO - Shutting down ARIA system...
2025-06-24 00:24:31,634 - aria_system.core.workflow_orchestrator - INFO - Stopping ARIA Workflow Orchestrator...
2025-06-24 00:24:31,634 - aria_system.services.email_monitor - INFO - Stopping email monitoring service...
2025-06-24 00:24:31,634 - main - INFO - ARIA system shutdown complete
2025-06-24 00:24:48,102 - __main__ - INFO - Starting ARIA API server on 0.0.0.0:8001
2025-06-24 00:24:48,124 - main - INFO - Starting ARIA Claims Processing System...
2025-06-24 00:24:48,243 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 00:24:48,293 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 00:24:48,380 - main - INFO - ARIA system started successfully
2025-06-24 00:24:48,380 - aria_system.core.workflow_orchestrator - INFO - Starting ARIA Workflow Orchestrator...
2025-06-24 00:24:48,382 - aria_system.services.email_monitor - INFO - Starting email monitoring service...
2025-06-24 00:24:50,370 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:24:50,371 - aria_system.services.slack_service - ERROR - Failed to start Slack bot: name 'AsyncSocketModeHandler' is not defined
2025-06-24 00:25:15,734 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=id&limit=1 "HTTP/2 404 Not Found"
2025-06-24 00:25:15,737 - aria_system.database.supabase_client - ERROR - Database health check failed: {'message': 'relation "public.claims" does not exist', 'code': '42P01', 'hint': None, 'details': None}
2025-06-24 00:25:22,444 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:25:38,161 - __main__ - INFO - Starting ARIA dashboard on localhost:8501
2025-06-24 00:25:54,063 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:26:26,255 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:26:57,755 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:27:29,192 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:28:01,448 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:28:32,588 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:29:04,834 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:29:36,270 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:30:08,424 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:30:40,495 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:30:42,372 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=id&limit=1 "HTTP/2 200 OK"
2025-06-24 00:31:12,465 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:31:44,833 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:32:03,940 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=id&limit=1 "HTTP/2 200 OK"
2025-06-24 00:32:16,883 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:32:24,755 - aria_system.core.workflow_orchestrator - ERROR - Error processing new claim: 'sender_email'
2025-06-24 00:32:48,327 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:33:20,475 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:33:52,819 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:34:24,166 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:34:55,704 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:35:14,191 - aria_system.core.workflow_orchestrator - ERROR - Error processing new claim: 'sender_email'
2025-06-24 00:35:27,791 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:35:59,823 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:36:25,559 - __main__ - INFO - Starting ARIA API server on 0.0.0.0:8001
2025-06-24 00:36:25,580 - main - INFO - Starting ARIA Claims Processing System...
2025-06-24 00:36:25,703 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 00:36:25,752 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 00:36:25,838 - main - INFO - ARIA system started successfully
2025-06-24 00:36:25,838 - aria_system.core.workflow_orchestrator - INFO - Starting ARIA Workflow Orchestrator...
2025-06-24 00:36:25,839 - aria_system.services.email_monitor - INFO - Starting email monitoring service...
