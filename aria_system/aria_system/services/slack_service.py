"""
ARIA Slack Bot Integration Service
Interactive Slack bot for claim assignment and team collaboration
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

import aiohttp
from slack_bolt.async_app import AsyncApp
from slack_bolt.adapter.socket_mode.async_handler import AsyncSock<PERSON><PERSON>odeHandler

from ..config import Settings
from ..database.supabase_client import SupabaseClient
from ..services.zendesk_service import ZendeskService

logger = logging.getLogger(__name__)


class SlackService:
    """Slack bot integration for claim processing and team collaboration"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.supabase = SupabaseClient(settings)
        self.zendesk = ZendeskService(settings)
        
        # Initialize Slack app
        self.app = AsyncApp(
            token=settings.slack.bot_token,
            signing_secret=settings.slack.signing_secret
        )
        
        # Setup event handlers
        self._setup_handlers()
        
        # Slack configuration
        self.claims_channel = settings.slack.claims_channel
        self.alerts_channel = settings.slack.alerts_channel
    
    def _setup_handlers(self):
        """Setup Slack event handlers"""
        
        @self.app.event("app_mention")
        async def handle_app_mention(event, say):
            """Handle app mentions"""
            await say(f"Hello <@{event['user']}>! I'm the ARIA Claims Bot. How can I help you?")
        
        @self.app.command("/claim")
        async def handle_claim_command(ack, respond, command):
            """Handle /claim slash command"""
            await ack()
            
            text = command.get('text', '').strip()
            if not text:
                await respond("Usage: `/claim <claim_number>` to get claim details")
                return
            
            claim_details = await self.get_claim_details(text)
            if claim_details:
                await respond(self._format_claim_details(claim_details))
            else:
                await respond(f"Claim `{text}` not found.")
        
        @self.app.action("assign_claim")
        async def handle_assign_claim(ack, body, respond):
            """Handle claim assignment button"""
            await ack()
            
            user_id = body["user"]["id"]
            claim_id = body["actions"][0]["value"]
            
            # Get user email from Slack
            user_email = await self._get_user_email(user_id)
            if user_email:
                success = await self._assign_claim_to_agent(claim_id, user_email, user_id)
                if success:
                    await respond(f"✅ Claim assigned to <@{user_id}>")
                else:
                    await respond("❌ Failed to assign claim. Please try again.")
            else:
                await respond("❌ Could not find your email address. Please contact admin.")
        
        @self.app.action("escalate_claim")
        async def handle_escalate_claim(ack, body, respond):
            """Handle claim escalation button"""
            await ack()
            
            claim_id = body["actions"][0]["value"]
            user_id = body["user"]["id"]
            
            success = await self._escalate_claim(claim_id, user_id)
            if success:
                await respond("🚨 Claim escalated to senior team")
            else:
                await respond("❌ Failed to escalate claim")
        
        @self.app.action("view_claim")
        async def handle_view_claim(ack, body, respond):
            """Handle view claim button"""
            await ack()
            
            claim_id = body["actions"][0]["value"]
            claim_details = await self.get_claim_details(claim_id)
            
            if claim_details:
                # Create modal with claim details
                modal = self._create_claim_modal(claim_details)
                await self.app.client.views_open(
                    trigger_id=body["trigger_id"],
                    view=modal
                )
            else:
                await respond("❌ Could not load claim details")
    
    async def start_bot(self):
        """Start the Slack bot"""
        try:
            handler = AsyncSocketModeHandler(self.app, self.settings.slack.app_token)
            await handler.start_async()
            logger.info("Slack bot started successfully")
        except Exception as e:
            logger.error(f"Failed to start Slack bot: {e}")
    
    async def send_new_claim_notification(self, claim_data: Dict) -> bool:
        """Send new claim notification to Slack channel"""
        try:
            blocks = self._create_new_claim_blocks(claim_data)
            
            response = await self.app.client.chat_postMessage(
                channel=self.claims_channel,
                text=f"🚨 New claim received: {claim_data['claim_number']}",
                blocks=blocks
            )
            
            if response["ok"]:
                logger.info(f"Sent new claim notification for {claim_data['claim_number']}")
                return True
            else:
                logger.error(f"Failed to send Slack notification: {response['error']}")
                return False
        
        except Exception as e:
            logger.error(f"Error sending Slack notification: {e}")
            return False
    
    async def send_claim_update(self, claim_data: Dict, update_message: str) -> bool:
        """Send claim status update to Slack"""
        try:
            blocks = self._create_update_blocks(claim_data, update_message)
            
            response = await self.app.client.chat_postMessage(
                channel=self.claims_channel,
                text=f"📋 Claim update: {claim_data['claim_number']}",
                blocks=blocks
            )
            
            return response["ok"]
        
        except Exception as e:
            logger.error(f"Error sending claim update: {e}")
            return False
    
    async def send_urgent_alert(self, claim_data: Dict, alert_message: str) -> bool:
        """Send urgent claim alert"""
        try:
            blocks = self._create_alert_blocks(claim_data, alert_message)
            
            response = await self.app.client.chat_postMessage(
                channel=self.alerts_channel,
                text=f"🚨 URGENT: {claim_data['claim_number']} - {alert_message}",
                blocks=blocks
            )
            
            return response["ok"]
        
        except Exception as e:
            logger.error(f"Error sending urgent alert: {e}")
            return False
    
    def _create_new_claim_blocks(self, claim_data: Dict) -> List[Dict]:
        """Create Slack blocks for new claim notification"""
        return [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": f"🚨 New Claim: {claim_data['claim_number']}"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": f"*Customer:* {claim_data.get('user_name', 'N/A')}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*Email:* {claim_data['user_email']}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*Priority:* {self._get_priority_emoji(claim_data.get('priority', 3))}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*Amount:* ${claim_data.get('claimed_amount', 'TBD')}"
                    }
                ]
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Subject:* {claim_data['subject']}\n*Description:* {claim_data['description'][:200]}..."
                }
            },
            {
                "type": "actions",
                "elements": [
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "text": "🙋‍♂️ Assign to Me"
                        },
                        "style": "primary",
                        "action_id": "assign_claim",
                        "value": claim_data['id']
                    },
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "text": "👁️ View Details"
                        },
                        "action_id": "view_claim",
                        "value": claim_data['id']
                    },
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "text": "🚨 Escalate"
                        },
                        "style": "danger",
                        "action_id": "escalate_claim",
                        "value": claim_data['id']
                    }
                ]
            },
            {
                "type": "context",
                "elements": [
                    {
                        "type": "mrkdwn",
                        "text": f"Zendesk: <{claim_data.get('zendesk_url', '#')}|Ticket #{claim_data.get('zendesk_ticket_id', 'N/A')}> | AI Confidence: {claim_data.get('ai_confidence_score', 0)*100:.1f}%"
                    }
                ]
            }
        ]
    
    def _create_update_blocks(self, claim_data: Dict, update_message: str) -> List[Dict]:
        """Create Slack blocks for claim updates"""
        return [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"📋 *{claim_data['claim_number']}* - {update_message}"
                }
            },
            {
                "type": "context",
                "elements": [
                    {
                        "type": "mrkdwn",
                        "text": f"Status: {claim_data['status']} | Assigned: {claim_data.get('assigned_agent_name', 'Unassigned')}"
                    }
                ]
            }
        ]
    
    def _create_alert_blocks(self, claim_data: Dict, alert_message: str) -> List[Dict]:
        """Create Slack blocks for urgent alerts"""
        return [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": f"🚨 URGENT ALERT"
                }
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Claim:* {claim_data['claim_number']}\n*Alert:* {alert_message}"
                }
            },
            {
                "type": "actions",
                "elements": [
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "text": "🔍 Investigate"
                        },
                        "style": "danger",
                        "action_id": "view_claim",
                        "value": claim_data['id']
                    }
                ]
            }
        ]
    
    def _create_claim_modal(self, claim_data: Dict) -> Dict:
        """Create modal view for claim details"""
        return {
            "type": "modal",
            "title": {
                "type": "plain_text",
                "text": f"Claim {claim_data['claim_number']}"
            },
            "blocks": [
                {
                    "type": "section",
                    "fields": [
                        {
                            "type": "mrkdwn",
                            "text": f"*Status:* {claim_data['status']}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Priority:* {self._get_priority_emoji(claim_data.get('priority', 3))}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Customer:* {claim_data.get('user_name', 'N/A')}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Email:* {claim_data['user_email']}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Amount:* ${claim_data.get('claimed_amount', 'TBD')}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*AI Confidence:* {claim_data.get('ai_confidence_score', 0)*100:.1f}%"
                        }
                    ]
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*Description:*\n{claim_data['description']}"
                    }
                }
            ]
        }
    
    def _get_priority_emoji(self, priority: int) -> str:
        """Get emoji for priority level"""
        priority_map = {
            1: "🔴 Urgent",
            2: "🟡 High", 
            3: "🟢 Normal",
            4: "⚪ Low"
        }
        return priority_map.get(priority, "🟢 Normal")
    
    async def _get_user_email(self, user_id: str) -> Optional[str]:
        """Get user email from Slack user ID"""
        try:
            response = await self.app.client.users_info(user=user_id)
            if response["ok"]:
                return response["user"]["profile"].get("email")
            return None
        except Exception as e:
            logger.error(f"Error getting user email: {e}")
            return None
    
    async def _assign_claim_to_agent(self, claim_id: str, agent_email: str, slack_user_id: str) -> bool:
        """Assign claim to agent"""
        try:
            # Update claim assignment in database
            success = await self.supabase.assign_claim(claim_id, agent_email)
            
            if success:
                # Add agent to Zendesk ticket
                claim_data = await self.supabase.get_claim(claim_id)
                if claim_data and claim_data.get('zendesk_ticket_id'):
                    await self.zendesk.assign_ticket(
                        claim_data['zendesk_ticket_id'],
                        agent_email,
                        f"Claim assigned via Slack by <@{slack_user_id}>"
                    )
                
                logger.info(f"Assigned claim {claim_id} to {agent_email}")
                return True
            
            return False
        
        except Exception as e:
            logger.error(f"Error assigning claim: {e}")
            return False
    
    async def _escalate_claim(self, claim_id: str, escalated_by: str) -> bool:
        """Escalate claim to senior team"""
        try:
            # Update claim status
            success = await self.supabase.escalate_claim(claim_id, escalated_by)
            
            if success:
                # Send alert to alerts channel
                claim_data = await self.supabase.get_claim(claim_id)
                await self.send_urgent_alert(
                    claim_data,
                    f"Escalated by <@{escalated_by}> - Requires senior review"
                )
                
                logger.info(f"Escalated claim {claim_id}")
                return True
            
            return False
        
        except Exception as e:
            logger.error(f"Error escalating claim: {e}")
            return False
    
    async def get_claim_details(self, claim_identifier: str) -> Optional[Dict]:
        """Get claim details by claim number or ID"""
        try:
            return await self.supabase.get_claim_by_number(claim_identifier)
        except Exception as e:
            logger.error(f"Error getting claim details: {e}")
            return None
    
    def _format_claim_details(self, claim_data: Dict) -> str:
        """Format claim details for Slack message"""
        return f"""
📋 *Claim Details: {claim_data['claim_number']}*

*Customer:* {claim_data.get('user_name', 'N/A')} ({claim_data['user_email']})
*Status:* {claim_data['status']}
*Priority:* {self._get_priority_emoji(claim_data.get('priority', 3))}
*Amount:* ${claim_data.get('claimed_amount', 'TBD')}
*AI Confidence:* {claim_data.get('ai_confidence_score', 0)*100:.1f}%
*Assigned:* {claim_data.get('assigned_agent_name', 'Unassigned')}

*Description:* {claim_data['description'][:300]}...

*Zendesk:* <{claim_data.get('zendesk_url', '#')}|Ticket #{claim_data.get('zendesk_ticket_id', 'N/A')}>
        """.strip()
