"""
ARIA Multi-Channel Notification Service
Unified notification system for Email, SMS, WhatsApp with templates
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
from jinja2 import Environment, FileSystemLoader

import aiohttp
from twilio.rest import Client as TwilioClient
import sendgrid
from sendgrid.helpers.mail import Mail, Email, To, Content

from ..config import Settings
from ..database.supabase_client import SupabaseClient

logger = logging.getLogger(__name__)


class NotificationService:
    """Multi-channel notification service for claim processing"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.supabase = SupabaseClient(settings)
        
        # Initialize notification providers
        self._init_email_service()
        self._init_sms_service()
        self._init_whatsapp_service()
        
        # Initialize template engine
        self._init_templates()
    
    def _init_email_service(self):
        """Initialize email service (SendGrid)"""
        try:
            self.sendgrid_client = sendgrid.SendGridAPIClient(
                api_key=self.settings.notifications.sendgrid_api_key
            )
            self.email_enabled = True
            logger.info("Email service initialized (SendGrid)")
        except Exception as e:
            logger.warning(f"Email service not available: {e}")
            self.email_enabled = False
    
    def _init_sms_service(self):
        """Initialize SMS service (Twilio)"""
        try:
            self.twilio_client = TwilioClient(
                self.settings.notifications.twilio_account_sid,
                self.settings.notifications.twilio_auth_token
            )
            self.sms_enabled = True
            logger.info("SMS service initialized (Twilio)")
        except Exception as e:
            logger.warning(f"SMS service not available: {e}")
            self.sms_enabled = False
    
    def _init_whatsapp_service(self):
        """Initialize WhatsApp service (Twilio)"""
        try:
            # WhatsApp uses same Twilio client
            self.whatsapp_enabled = (
                self.sms_enabled and 
                self.settings.notifications.whatsapp_enabled
            )
            if self.whatsapp_enabled:
                logger.info("WhatsApp service initialized (Twilio)")
        except Exception as e:
            logger.warning(f"WhatsApp service not available: {e}")
            self.whatsapp_enabled = False
    
    def _init_templates(self):
        """Initialize Jinja2 template engine"""
        template_dir = Path(__file__).parent.parent / "templates" / "notifications"
        template_dir.mkdir(parents=True, exist_ok=True)
        
        self.template_env = Environment(
            loader=FileSystemLoader(str(template_dir)),
            autoescape=True
        )
        
        # Create default templates if they don't exist
        self._create_default_templates(template_dir)
    
    def _create_default_templates(self, template_dir: Path):
        """Create default notification templates"""
        templates = {
            "claim_received_email.html": """
            <h2>Claim Received - {{ claim_number }}</h2>
            <p>Dear {{ user_name or 'Valued Customer' }},</p>
            <p>We have received your insurance claim and are processing it immediately.</p>
            <p><strong>Claim Reference:</strong> {{ claim_number }}</p>
            <p><strong>Status:</strong> Documents received and under verification</p>
            <p><a href="{{ tracking_link }}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Track Your Claim</a></p>
            <p>You will receive updates at each step of the process.</p>
            <p>Best regards,<br>ARIA Claims Team</p>
            """,
            
            "claim_received_sms.txt": """
            ARIA Claims: Your claim {{ claim_number }} has been received. Track progress: {{ tracking_link }}
            """,
            
            "claim_received_whatsapp.txt": """
            ✅ Your insurance claim has been received!
            
            📋 Reference: {{ claim_number }}
            🔍 Status: Under verification
            📱 Track: {{ tracking_link }}
            
            You'll get updates at each step.
            """,
            
            "agent_assignment_email.html": """
            <h2>New Claim Assignment - {{ claim_number }}</h2>
            <p>Hello {{ agent_name }},</p>
            <p>A new claim has been assigned to you for review.</p>
            <p><strong>Claim:</strong> {{ claim_number }}</p>
            <p><strong>Customer:</strong> {{ customer_name }} ({{ customer_email }})</p>
            <p><strong>Priority:</strong> {{ priority }}</p>
            <p><strong>AI Confidence:</strong> {{ ai_confidence }}%</p>
            <p><a href="{{ review_link }}" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Review Claim</a></p>
            <p>Zendesk Ticket: <a href="{{ zendesk_url }}">{{ zendesk_ticket_id }}</a></p>
            """,
            
            "status_update_email.html": """
            <h2>Claim Update - {{ claim_number }}</h2>
            <p>Dear {{ user_name or 'Valued Customer' }},</p>
            <p>Your claim status has been updated:</p>
            <p><strong>New Status:</strong> {{ new_status }}</p>
            <p><strong>Update:</strong> {{ update_message }}</p>
            {% if eta %}
            <p><strong>Expected completion:</strong> {{ eta }}</p>
            {% endif %}
            <p><a href="{{ tracking_link }}">View Full Details</a></p>
            """,
            
            "approval_decision_email.html": """
            <h2>Claim Decision - {{ claim_number }}</h2>
            <p>Dear {{ user_name or 'Valued Customer' }},</p>
            {% if decision == 'approved' %}
            <p style="color: green;"><strong>✅ Your claim has been APPROVED</strong></p>
            <p><strong>Approved Amount:</strong> ${{ approved_amount }}</p>
            <p>Payment will be processed within 3-5 business days.</p>
            {% elif decision == 'denied' %}
            <p style="color: red;"><strong>❌ Your claim has been DENIED</strong></p>
            <p><strong>Reason:</strong> {{ denial_reason }}</p>
            <p>You have the right to appeal this decision.</p>
            {% endif %}
            <p><a href="{{ tracking_link }}">View Complete Details</a></p>
            """
        }
        
        for filename, content in templates.items():
            template_file = template_dir / filename
            if not template_file.exists():
                template_file.write_text(content.strip())
    
    async def send_multi_channel_notification(
        self,
        user_email: str,
        user_phone: Optional[str],
        message_type: str,
        context: Dict[str, Any],
        channels: List[str] = None
    ):
        """Send notification across multiple channels"""
        if channels is None:
            channels = ['email']
            if user_phone:
                channels.extend(['sms', 'whatsapp'])
        
        results = {}
        
        # Send email notification
        if 'email' in channels and self.email_enabled:
            try:
                email_result = await self.send_email(
                    to_email=user_email,
                    template=f"{message_type}_email",
                    context=context
                )
                results['email'] = email_result
            except Exception as e:
                logger.error(f"Email notification failed: {e}")
                results['email'] = False
        
        # Send SMS notification
        if 'sms' in channels and user_phone and self.sms_enabled:
            try:
                sms_result = await self.send_sms(
                    to_phone=user_phone,
                    template=f"{message_type}_sms",
                    context=context
                )
                results['sms'] = sms_result
            except Exception as e:
                logger.error(f"SMS notification failed: {e}")
                results['sms'] = False
        
        # Send WhatsApp notification
        if 'whatsapp' in channels and user_phone and self.whatsapp_enabled:
            try:
                whatsapp_result = await self.send_whatsapp(
                    to_phone=user_phone,
                    template=f"{message_type}_whatsapp",
                    context=context
                )
                results['whatsapp'] = whatsapp_result
            except Exception as e:
                logger.error(f"WhatsApp notification failed: {e}")
                results['whatsapp'] = False
        
        # Log notification results
        await self._log_notifications(user_email, message_type, results, context)
        
        return results
    
    async def send_email(
        self,
        to_email: str,
        subject: str = None,
        template: str = None,
        context: Dict[str, Any] = None,
        html_content: str = None
    ) -> bool:
        """Send email notification"""
        try:
            if template:
                # Render template
                template_obj = self.template_env.get_template(f"{template}.html")
                html_content = template_obj.render(context or {})
                
                # Generate subject from context if not provided
                if not subject:
                    subject = self._generate_email_subject(template, context or {})
            
            # Create email
            from_email = Email(
                self.settings.notifications.sendgrid_from_email,
                self.settings.email.from_name
            )
            to_email_obj = To(to_email)
            content = Content("text/html", html_content)
            
            mail = Mail(from_email, to_email_obj, subject, content)
            
            # Send email
            response = self.sendgrid_client.send(mail)
            
            if response.status_code in [200, 202]:
                logger.info(f"Email sent successfully to {to_email}")
                return True
            else:
                logger.error(f"Email failed with status {response.status_code}")
                return False
        
        except Exception as e:
            logger.error(f"Error sending email to {to_email}: {e}")
            return False
    
    async def send_sms(
        self,
        to_phone: str,
        message: str = None,
        template: str = None,
        context: Dict[str, Any] = None
    ) -> bool:
        """Send SMS notification"""
        try:
            if template:
                # Render template
                template_obj = self.template_env.get_template(f"{template}.txt")
                message = template_obj.render(context or {})
            
            # Send SMS
            message_obj = self.twilio_client.messages.create(
                body=message,
                from_=self.settings.notifications.twilio_phone_number,
                to=to_phone
            )
            
            logger.info(f"SMS sent successfully to {to_phone}: {message_obj.sid}")
            return True
        
        except Exception as e:
            logger.error(f"Error sending SMS to {to_phone}: {e}")
            return False
    
    async def send_whatsapp(
        self,
        to_phone: str,
        message: str = None,
        template: str = None,
        context: Dict[str, Any] = None
    ) -> bool:
        """Send WhatsApp notification"""
        try:
            if template:
                # Render template
                template_obj = self.template_env.get_template(f"{template}.txt")
                message = template_obj.render(context or {})
            
            # Format phone numbers for WhatsApp
            from_whatsapp = self.settings.notifications.whatsapp_from_number
            to_whatsapp = f"whatsapp:{to_phone}"
            
            # Send WhatsApp message
            message_obj = self.twilio_client.messages.create(
                body=message,
                from_=from_whatsapp,
                to=to_whatsapp
            )
            
            logger.info(f"WhatsApp sent successfully to {to_phone}: {message_obj.sid}")
            return True
        
        except Exception as e:
            logger.error(f"Error sending WhatsApp to {to_phone}: {e}")
            return False
    
    async def send_slack_notification(
        self,
        channel: str,
        message: str,
        attachments: List[Dict] = None,
        blocks: List[Dict] = None
    ) -> bool:
        """Send Slack notification"""
        try:
            webhook_url = self.settings.slack.webhook_url
            
            payload = {
                "channel": channel,
                "text": message,
                "username": "ARIA Claims Bot",
                "icon_emoji": ":robot_face:"
            }
            
            if attachments:
                payload["attachments"] = attachments
            
            if blocks:
                payload["blocks"] = blocks
            
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=payload) as response:
                    if response.status == 200:
                        logger.info(f"Slack notification sent to {channel}")
                        return True
                    else:
                        logger.error(f"Slack notification failed: {response.status}")
                        return False
        
        except Exception as e:
            logger.error(f"Error sending Slack notification: {e}")
            return False
    
    def _generate_email_subject(self, template: str, context: Dict) -> str:
        """Generate email subject based on template and context"""
        subjects = {
            "claim_received": f"Claim Received - {context.get('claim_number', 'New Claim')}",
            "status_update": f"Claim Update - {context.get('claim_number', 'Your Claim')}",
            "agent_assignment": f"New Claim Assignment - {context.get('claim_number', '')}",
            "approval_decision": f"Claim Decision - {context.get('claim_number', 'Your Claim')}",
            "request_attachments": "Additional Documents Required for Your Claim",
            "claim_guidance": "How to Submit Your Insurance Claim"
        }
        
        template_key = template.replace("_email", "")
        return subjects.get(template_key, "ARIA Claims Notification")
    
    async def _log_notifications(
        self,
        recipient: str,
        message_type: str,
        results: Dict,
        context: Dict
    ):
        """Log notification results to database"""
        try:
            for channel, success in results.items():
                notification_data = {
                    'recipient': recipient,
                    'channel': channel,
                    'message_type': message_type,
                    'status': 'sent' if success else 'failed',
                    'context': context,
                    'sent_at': datetime.now() if success else None
                }
                
                await self.supabase.log_notification(notification_data)
        
        except Exception as e:
            logger.error(f"Error logging notifications: {e}")
