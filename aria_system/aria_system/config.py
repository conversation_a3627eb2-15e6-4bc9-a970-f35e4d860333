"""
Configuration management for ARIA system.
Handles environment variables, settings validation, and configuration loading.
"""

import os
from pathlib import Path
from typing import Optional, List
from pydantic import Field, validator
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""

    # Supabase configuration
    supabase_url: str = Field(default="https://demo.supabase.co")
    supabase_anon_key: str = Field(default="demo_anon_key")
    supabase_service_role_key: str = Field(default="demo_service_role_key")

    # Traditional database settings
    url: str = Field(default="postgresql://aria_user:aria_password@localhost:5432/aria_db")
    redis_url: str = Field(default="redis://localhost:6379/0")
    pool_size: int = Field(default=10)
    max_overflow: int = Field(default=20)
    pool_timeout: int = Field(default=30)

    # Schema file location
    schema_file: Optional[Path] = Field(default=None)

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if self.schema_file is None:
            self.schema_file = Path(__file__).parent.parent / "database" / "supabase_schema.sql"

    class Config:
        env_prefix = ""
        extra = "allow"


class AISettings(BaseSettings):
    """AI and ML configuration settings."""

    # OpenAI Configuration
    openai_api_key: str = Field(default="demo_key_placeholder")
    openai_model: str = Field(default="gpt-4-turbo-preview")
    openai_temperature: float = Field(default=0.1)
    openai_max_tokens: int = Field(default=4000)

    # Zurich OCR API Configuration
    zurich_ocr_api_url: str = Field(default="https://zurich-ocr.dev-scc-demo.rozie.ai/api/v1/batch-process")
    zurich_ocr_api_key: Optional[str] = Field(default=None)

    # HumanLayer settings
    humanlayer_api_key: str = Field(default="demo_humanlayer_key")
    humanlayer_run_id: str = Field(default="aria-claims-processor")

    class Config:
        env_prefix = ""
        extra = "allow"


class EmailSettings(BaseSettings):
    """Email processing configuration."""

    claims_email: str = Field(default="<EMAIL>")
    claims_password: str = Field(default="demo_password")
    imap_server: str = Field(default="imap.gmail.com")
    smtp_server: str = Field(default="smtp.gmail.com")
    imap_port: int = Field(default=993)
    smtp_port: int = Field(default=587)
    check_interval: int = Field(default=30)  # seconds

    class Config:
        env_prefix = ""
        extra = "allow"


class AzureSettings(BaseSettings):
    """Azure cloud services configuration."""

    storage_connection_string: Optional[str] = None
    cv_endpoint: Optional[str] = None
    cv_key: Optional[str] = None
    communication_connection_string: Optional[str] = None
    email_sender: Optional[str] = None

    class Config:
        env_prefix = "AZURE_"
        extra = "allow"


class AWSSettings(BaseSettings):
    """AWS cloud services configuration."""

    access_key_id: Optional[str] = None
    secret_access_key: Optional[str] = None
    region: str = Field(default="us-east-1")
    s3_bucket: Optional[str] = None
    email_sender: Optional[str] = None

    class Config:
        env_prefix = "AWS_"
        extra = "allow"


class GCPSettings(BaseSettings):
    """Google Cloud Platform configuration."""

    application_credentials: Optional[str] = None
    project_id: Optional[str] = None
    processor_name: Optional[str] = None
    storage_bucket: Optional[str] = None

    class Config:
        env_prefix = "GCP_"
        extra = "allow"


class ApplicationSettings(BaseSettings):
    """Main application configuration."""
    
    name: str = Field(default="ARIA Claims Processor")
    version: str = Field(default="1.0.0")
    debug: bool = Field(default=False)
    log_level: str = Field(default="INFO")
    environment: str = Field(default="development")
    
    # API settings
    api_host: str = Field(default="localhost")
    api_port: int = Field(default=8000)
    base_url: str = Field(default="http://localhost:8000")
    domain: str = Field(default="localhost")

    # Dashboard settings
    dashboard_host: str = Field(default="localhost")
    dashboard_port: int = Field(default=8501)

    # CORS settings
    allowed_origins: str = Field(default="http://localhost:3000,http://localhost:8000,http://localhost:8501")
    
    # Security
    secret_key: str = Field(default="demo-secret-key-for-aria-system-demo-only")
    jwt_secret: str = Field(default="demo-jwt-secret-for-aria-system-demo-only")
    encryption_key: str = Field(default="demo-encryption-key-for-aria-system-demo-only")
    
    # Feature flags
    enable_multi_cloud: bool = Field(default=True)
    enable_auto_approval: bool = Field(default=True)
    enable_fraud_detection: bool = Field(default=True)
    enable_real_time_updates: bool = Field(default=True)
    
    # Monitoring
    enable_metrics: bool = Field(default=True)
    metrics_port: int = Field(default=9090)
    health_check_interval: int = Field(default=30)
    
    class Config:
        env_prefix = "APP_"
        extra = "allow"
        
    @validator("log_level")
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of {valid_levels}")
        return v.upper()


class NotificationSettings(BaseSettings):
    """Notification services configuration."""

    # Twilio SMS/WhatsApp
    twilio_account_sid: str = Field(default="demo_twilio_sid")
    twilio_auth_token: str = Field(default="demo_twilio_token")
    twilio_phone_number: str = Field(default="+**********")
    whatsapp_from_number: str = Field(default="whatsapp:+***********")
    whatsapp_enabled: bool = Field(default=True)

    # SendGrid Email
    sendgrid_api_key: str = Field(default="demo_sendgrid_key")
    sendgrid_from_email: str = Field(default="<EMAIL>")

    class Config:
        env_prefix = ""
        extra = "allow"


class ZendeskSettings(BaseSettings):
    """Zendesk integration configuration."""

    subdomain: str = Field(default="demo-subdomain")
    email: str = Field(default="<EMAIL>")
    api_token: str = Field(default="demo_zendesk_token")
    group_id: str = Field(default="demo_group_id")
    priority: str = Field(default="normal")
    type: str = Field(default="incident")
    tags: str = Field(default="aria,claims,automated")

    class Config:
        env_prefix = "ZENDESK_"
        extra = "allow"


class SlackSettings(BaseSettings):
    """Slack integration configuration."""

    bot_token: str = Field(default="xoxb-demo-slack-bot-token")
    app_token: str = Field(default="xapp-demo-slack-app-token")
    signing_secret: str = Field(default="demo_slack_signing_secret")
    webhook_url: str = Field(default="https://hooks.slack.com/services/demo/webhook/url")
    claims_channel: str = Field(default="#claims-processing")
    alerts_channel: str = Field(default="#claims-alerts")

    class Config:
        env_prefix = "SLACK_"
        extra = "allow"


class Settings(BaseSettings):
    """Main settings class combining all configuration sections."""

    database: DatabaseSettings = DatabaseSettings()
    ai: AISettings = AISettings()
    email: EmailSettings = EmailSettings()
    notifications: NotificationSettings = NotificationSettings()
    zendesk: ZendeskSettings = ZendeskSettings()
    slack: SlackSettings = SlackSettings()
    azure: AzureSettings = AzureSettings()
    aws: AWSSettings = AWSSettings()
    gcp: GCPSettings = GCPSettings()
    app: ApplicationSettings = ApplicationSettings()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "allow"
        
    def get_database_url(self) -> str:
        """Get the database URL."""
        return self.database.url
    
    def get_redis_url(self) -> str:
        """Get the Redis URL."""
        return self.database.redis_url
    
    def is_development(self) -> bool:
        """Check if running in development mode."""
        return self.app.environment.lower() == "development"
    
    def is_production(self) -> bool:
        """Check if running in production mode."""
        return self.app.environment.lower() == "production"
    
    def get_enabled_cloud_providers(self) -> List[str]:
        """Get list of enabled cloud providers."""
        providers = []
        
        if self.azure.storage_connection_string:
            providers.append("azure")
        if self.aws.access_key_id:
            providers.append("aws")
        if self.gcp.application_credentials:
            providers.append("gcp")
            
        return providers


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings


def reload_settings() -> Settings:
    """Reload settings from environment."""
    global settings
    settings = Settings()
    return settings
