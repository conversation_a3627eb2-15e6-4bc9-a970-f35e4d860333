"""
Configuration management for ARIA system.
Handles environment variables, settings validation, and configuration loading.
"""

import os
from typing import Optional, List
from pydantic import BaseSettings, Field, validator
from pydantic_settings import BaseSettings as PydanticBaseSettings


class DatabaseSettings(PydanticBaseSettings):
    """Database configuration settings."""
    
    url: str = Field(default="postgresql://aria_user:aria_password@localhost:5432/aria_db")
    redis_url: str = Field(default="redis://localhost:6379/0")
    pool_size: int = Field(default=10)
    max_overflow: int = Field(default=20)
    pool_timeout: int = Field(default=30)
    
    class Config:
        env_prefix = "DATABASE_"


class AISettings(PydanticBaseSettings):
    """AI and ML configuration settings."""
    
    openai_api_key: str = Field(...)
    openai_model: str = Field(default="gpt-4-turbo-preview")
    openai_temperature: float = Field(default=0.1)
    openai_max_tokens: int = Field(default=4000)
    
    # HumanLayer settings
    humanlayer_api_key: str = Field(...)
    humanlayer_run_id: str = Field(default="aria-claims-processor")
    
    class Config:
        env_prefix = ""


class EmailSettings(PydanticBaseSettings):
    """Email processing configuration."""
    
    claims_email: str = Field(...)
    claims_password: str = Field(...)
    imap_server: str = Field(default="imap.gmail.com")
    smtp_server: str = Field(default="smtp.gmail.com")
    imap_port: int = Field(default=993)
    smtp_port: int = Field(default=587)
    check_interval: int = Field(default=30)  # seconds
    
    class Config:
        env_prefix = ""


class AzureSettings(PydanticBaseSettings):
    """Azure cloud services configuration."""
    
    storage_connection_string: Optional[str] = None
    cv_endpoint: Optional[str] = None
    cv_key: Optional[str] = None
    communication_connection_string: Optional[str] = None
    email_sender: Optional[str] = None
    
    class Config:
        env_prefix = "AZURE_"


class AWSSettings(PydanticBaseSettings):
    """AWS cloud services configuration."""
    
    access_key_id: Optional[str] = None
    secret_access_key: Optional[str] = None
    region: str = Field(default="us-east-1")
    s3_bucket: Optional[str] = None
    email_sender: Optional[str] = None
    
    class Config:
        env_prefix = "AWS_"


class GCPSettings(PydanticBaseSettings):
    """Google Cloud Platform configuration."""
    
    application_credentials: Optional[str] = None
    project_id: Optional[str] = None
    processor_name: Optional[str] = None
    storage_bucket: Optional[str] = None
    
    class Config:
        env_prefix = "GCP_"


class ApplicationSettings(PydanticBaseSettings):
    """Main application configuration."""
    
    name: str = Field(default="ARIA Claims Processor")
    version: str = Field(default="1.0.0")
    debug: bool = Field(default=False)
    log_level: str = Field(default="INFO")
    environment: str = Field(default="development")
    
    # API settings
    api_host: str = Field(default="localhost")
    api_port: int = Field(default=8000)
    
    # Dashboard settings
    dashboard_host: str = Field(default="localhost")
    dashboard_port: int = Field(default=8501)
    
    # Security
    secret_key: str = Field(...)
    jwt_secret: str = Field(...)
    encryption_key: str = Field(...)
    
    # Feature flags
    enable_multi_cloud: bool = Field(default=True)
    enable_auto_approval: bool = Field(default=True)
    enable_fraud_detection: bool = Field(default=True)
    enable_real_time_updates: bool = Field(default=True)
    
    # Monitoring
    enable_metrics: bool = Field(default=True)
    metrics_port: int = Field(default=9090)
    health_check_interval: int = Field(default=30)
    
    class Config:
        env_prefix = "APP_"
        
    @validator("log_level")
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of {valid_levels}")
        return v.upper()


class Settings(PydanticBaseSettings):
    """Main settings class combining all configuration sections."""
    
    database: DatabaseSettings = DatabaseSettings()
    ai: AISettings = AISettings()
    email: EmailSettings = EmailSettings()
    azure: AzureSettings = AzureSettings()
    aws: AWSSettings = AWSSettings()
    gcp: GCPSettings = GCPSettings()
    app: ApplicationSettings = ApplicationSettings()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    def get_database_url(self) -> str:
        """Get the database URL."""
        return self.database.url
    
    def get_redis_url(self) -> str:
        """Get the Redis URL."""
        return self.database.redis_url
    
    def is_development(self) -> bool:
        """Check if running in development mode."""
        return self.app.environment.lower() == "development"
    
    def is_production(self) -> bool:
        """Check if running in production mode."""
        return self.app.environment.lower() == "production"
    
    def get_enabled_cloud_providers(self) -> List[str]:
        """Get list of enabled cloud providers."""
        providers = []
        
        if self.azure.storage_connection_string:
            providers.append("azure")
        if self.aws.access_key_id:
            providers.append("aws")
        if self.gcp.application_credentials:
            providers.append("gcp")
            
        return providers


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings


def reload_settings() -> Settings:
    """Reload settings from environment."""
    global settings
    settings = Settings()
    return settings
